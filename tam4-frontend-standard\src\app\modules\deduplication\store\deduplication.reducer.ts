import {ObjectsUtils} from 'src/app/utils';
import {RelationshipRole, RelationshipType} from '../../relationships/endpoint/relationships.model';
import {
    applyMaterialSecondarySelection,
    applyPlantSecondarySelection,
    getPlantInformationFromStepPage,
    updateSelectedDetailsOnState
} from '../common/deduplication-utils';
import {
    DeduplicationBasicDataRowDto,
    DeduplicationEnrichedDataDetails,
    DeduplicationFieldConfiguration,
    DeduplicationViewModeEnum,
    MaterialInRelationship,
    RelationshipMaterialInfo,
} from '../models/deduplication.models';
import {
    DEDUPLICATION_ACTION_NAMES,
    DeduplicationAction_BulkSelectEnrichmentColumn,
    DeduplicationAction_BulkSelectPlantEnrichmentColumn,
    DeduplicationAction_GetEnrichmentMaterialDetails_Success,
    DeduplicationAction_GetEnrichmentPlantDetails_Success,
    DeduplicationAction_GetMasterDataStatus_Success,
    DeduplicationAction_GetRelationshipMaterialsDetails_Success,
    DeduplicationAction_Init_Stepper,
    DeduplicationAction_Init_Stepper_Success,
    DeduplicationAction_OpenAdditionalInfo_Success,
    DeduplicationAction_PlantEnrichmentSetup_Success,
    DeduplicationAction_RelationshipCreationTogglePrimary,
    DeduplicationAction_RelationshipCreationToggleSelected,
    DeduplicationAction_RelationshipTypeSelection,
    DeduplicationAction_SelectedSubtypesRelationships,
    DeduplicationAction_SetCreatingGoldenRecord,
    DeduplicationAction_SetMaterialIds,
    DeduplicationAction_SetRelationshipValidateError,
    DeduplicationAction_SetRequestNote,
    DeduplicationAction_SetSlaveStatusInARelationship,
    DeduplicationAction_SetStepPage,
    DeduplicationAction_SubtypesRelationships,
    DeduplicationAction_ToggleShowEnrichedData,
    DeduplicationAction_UncheckPlantSecondaryFieldsForPrimaryField,
    DeduplicationAction_UncheckSecondaryFieldsForPrimaryField,
    DeduplicationAction_UpdateCurrentClient,
    DeduplicationAction_UpdateCurrentPlant,
    DeduplicationAction_UpdateEnrichmentFieldErrors,
    DeduplicationAction_UpdateEnrichmentFieldSelection,
    DeduplicationAction_UpdateEnrichmentPrimaryFieldValue,
    DeduplicationAction_UpdatePlantEnrichmentFieldSelection,
    DeduplicationAction_UpdatePlantEnrichmentPrimaryFieldValue,
    DeduplicationAction_ValidateDeduplication_Failure,
    DeduplicationAction_ValidateDeduplicationStep_Failure,
    DeduplicationAction_ValidateDeduplicationStep_Success,
    DeduplicationActionTypes
} from './deduplication.actions';
import {DeduplicationState, TAM_DEDUPLICATION_INITIAL_STATE} from './deduplication.state';

export class DeduplicationReducer {
    public static reduce(state: DeduplicationState = TAM_DEDUPLICATION_INITIAL_STATE,
                         action: DeduplicationActionTypes = {type: null}): DeduplicationState {
        switch (action.type) {
            case DEDUPLICATION_ACTION_NAMES.INIT:
                return DeduplicationReducer.doInit(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_STEPPER:
                return DeduplicationReducer.doInitStepper(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_STEPPER_SUCCESS:
                return DeduplicationReducer.doInitStepperSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_MATERIAL_IDS:
                return DeduplicationReducer.doSetMaterialIds(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_TYPE_SELECTION:
                return DeduplicationReducer.doRelationshipTypeSelection(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_SLAVE_STATUS_IN_A_RELATIONSHIP:
                 return DeduplicationReducer.doSetSlaveStatusInARelationship(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_STEP_PAGE:
                 return DeduplicationReducer.doSetStepPage(state, action);
           case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS:
                 return DeduplicationReducer.doGetRelationshipMaterialsDetailsSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED:
                 return DeduplicationReducer.doToggleSelected(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY:
                 return DeduplicationReducer.doTogglePrimary(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD:
                 return DeduplicationReducer.doSetCreatingGoldenRecord(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS:
                 return DeduplicationReducer.doGetSubtypesRelationship(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_SUCCESS:
                 return DeduplicationReducer.doGetSubtypesRelationshipSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_FAILURE:
                 return DeduplicationReducer.doGetSubtypesRelationshipFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.SELECTED_SUBTYPES_RELATIONSHIPS:
                 return DeduplicationReducer.doSelectedSubtypesRelationships(state, action);
           case DEDUPLICATION_ACTION_NAMES.SET_RELATIONSHIP_VALIDATE_ERROR:
               return DeduplicationReducer.doSetRelationshipValidateError(state, action);
           case DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_IS_VALID:
               return DeduplicationReducer.doRelationshipIsValid(state, action);
           case DEDUPLICATION_ACTION_NAMES.CLEAR_RELATIONSHIP_VALIDATE_ERROR:
               return DeduplicationReducer.doClearRelationshipValidateError(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP_SUCCESS:
               return DeduplicationReducer.doValidateDeduplicationStepSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP_FAILURE:
               return DeduplicationReducer.doValidateDeduplicationStepFailure(state, action);
           case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_FAILURE:
               return DeduplicationReducer.doValidateDeduplicationFailure(state, action);
            case DEDUPLICATION_ACTION_NAMES.INIT_LICENSE_CONFIGURATION:
                return DeduplicationReducer.doInitLicenseConfiguration(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS:
                return DeduplicationReducer.doGetEnrichmentMaterialDetailsSuccess(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS_SUCCESS:
                return DeduplicationReducer.doGetEnrichmentPlantDetailsSuccess(state, action);
            case DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP_SUCCESS:
                return DeduplicationReducer.doPlantEnrichmentSetupSuccess(state, action);
            case DEDUPLICATION_ACTION_NAMES.UPDATE_CURRENT_CLIENT:
                 return DeduplicationReducer.doUpdateCurrentClient(state, action);
            case DEDUPLICATION_ACTION_NAMES.UPDATE_CURRENT_PLANT:
                return DeduplicationReducer.doUpdateCurrentPlant(state, action);
            case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_SUCCESS:
                 return DeduplicationReducer.doGetMasterDataStatusSuccess(state, action);
            case DEDUPLICATION_ACTION_NAMES.SET_REQUEST_NOTE:
                 return DeduplicationReducer.doSetRequestNote(state, action);
            case DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_INFO:
                return DeduplicationReducer.doOpenAdditionalInfo(state, action);
            case DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_INFO_SUCCESS:
                return DeduplicationReducer.doOpenAdditionalInfoSuccess(state, action);
           case DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_FIELD_SELECTION:
                 return DeduplicationReducer.doUpdateEnrichmentFieldSelection(state, action);
           case DEDUPLICATION_ACTION_NAMES.BULK_SELECT_ENRICHMENT_COLUMN:
               return DeduplicationReducer.doBulkSelectEnrichmentColumn(state, action);
         case DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_PRIMARY_FIELD_VALUE:
               return DeduplicationReducer.doUpdateEnrichmentPrimaryFieldValue(state, action);
         case DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_FIELD_ERRORS:
               return DeduplicationReducer.doUpdateEnrichmentFieldErrors(state, action);
         case DEDUPLICATION_ACTION_NAMES.UNCHECK_SECONDARY_FIELDS_FOR_PRIMARY_FIELD:
               return DeduplicationReducer.doUncheckSecondaryFieldsForPrimaryField(state, action);
         case DEDUPLICATION_ACTION_NAMES.UPDATE_PLANT_ENRICHMENT_FIELD_SELECTION:
               return DeduplicationReducer.doUpdatePlantEnrichmentFieldSelection(state, action);
         case DEDUPLICATION_ACTION_NAMES.BULK_SELECT_PLANT_ENRICHMENT_COLUMN:
               return DeduplicationReducer.doBulkSelectPlantEnrichmentColumn(state, action);
         case DEDUPLICATION_ACTION_NAMES.UPDATE_PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE:
               return DeduplicationReducer.doUpdatePlantEnrichmentPrimaryFieldValue(state, action);
         case DEDUPLICATION_ACTION_NAMES.UNCHECK_PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD:
               return DeduplicationReducer.doUncheckPlantSecondaryFieldsForPrimaryField(state, action);
         case DEDUPLICATION_ACTION_NAMES.CLEAR_STATUS_VALUES:
               return DeduplicationReducer.doClearStatusValues(state, action);
          case DEDUPLICATION_ACTION_NAMES.CLEAR_ALL_DATA:
               return DeduplicationReducer.doClearAllData(state);
          case DEDUPLICATION_ACTION_NAMES.TOGGLE_SHOW_ENRICHED_DATA:
              return DeduplicationReducer.doToggleShowEnrichedData(state, action);
          case DEDUPLICATION_ACTION_NAMES.TOGGLE_SHOW_ENRICHED_PLANT_DATA:
              return DeduplicationReducer.doToggleShowEnrichedPlantData(state, action);
          case DEDUPLICATION_ACTION_NAMES.MOVE_ALL_SELECTED_VALUES:
              return DeduplicationReducer.doMoveAllSelectedValues(state, action);
          case DEDUPLICATION_ACTION_NAMES.MOVE_ALL_SELECTED_PLANT_VALUES:
              return DeduplicationReducer.doMoveAllSelectedPlantValues(state, action);
          case DEDUPLICATION_ACTION_NAMES.SKIP_PLANT_STEP_SUCCESS:
              return DeduplicationReducer.doSkipPlantStepSuccess(state, action);
          case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS:
          case DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS:
          case DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP:
          case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS:
          case DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_STEP:
          case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS:
          case DEDUPLICATION_ACTION_NAMES.VALIDATE_MOVE_ALL_SELECTED_VALUES:
          case DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST:
            return DeduplicationReducer.doDefaultLoadingUpdate(state, true);
          case DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST_SUCCESS:
          case DEDUPLICATION_ACTION_NAMES.VALIDATE_MOVE_ALL_SELECTED_VALUES_SUCCESS:
          case DEDUPLICATION_ACTION_NAMES.VALIDATE_MOVE_ALL_SELECTED_PLANT_VALUES_SUCCESS:
            return DeduplicationReducer.doDefaultLoadingUpdate(state, false);
          case DEDUPLICATION_ACTION_NAMES.VALIDATE_MOVE_ALL_SELECTED_VALUES_FAILURE:
          case DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE:
          case DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_FAILURE:
          case DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST_FAILURE:
            return DeduplicationReducer.doDefaultFailure(state, action);
          default:
            return state;
       }
   }

   public static doInit(state: DeduplicationState, action: DeduplicationActionTypes) {
      return TAM_DEDUPLICATION_INITIAL_STATE;
   }

    public static doDefaultFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.loading = false;
        return newState;
    }

    public static doDefaultLoadingUpdate(
        state: DeduplicationState,
        isLoading: boolean
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.loading = isLoading;
        return newState;
    }

    public static doInitStepper(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_Init_Stepper = ObjectsUtils.forceCast<DeduplicationAction_Init_Stepper>(action);
        newState.loading = true;
        return newState;
    }

    public static doInitStepperSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_Init_Stepper_Success = ObjectsUtils.forceCast<DeduplicationAction_Init_Stepper_Success>(action);
        newState.stepper = a.payload;
        newState.step = newState.step ?? newState.stepper[0];
        newState.loading = false;
        return newState;
    }

    public static doSetMaterialIds(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_SetMaterialIds>(action);
        newState.materialIds = a.payload;
        const prevById = new Map(
            (newState.deduplicationRequest?.materialsInRelationship ?? []).map(m => [m.materialId, m])
        );

        newState.deduplicationRequest = {
            ...newState.deduplicationRequest,
            materialsInRelationship: a.payload.map(materialId => {
                const existing = prevById.get(materialId);
                if (existing) {
                    return { ...existing };
                }
                return {
                    materialId,
                    role: null,
                    primary: false,
                    selected: false,
                    status: null,
                    materialStatus: null
                };
            })
        };

        return newState;
    }

   public static doRelationshipTypeSelection(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipTypeSelection = ObjectsUtils.forceCast<DeduplicationAction_RelationshipTypeSelection>(action);
      const subtypeRelationship = newState.subtypeRelationship
          ?.filter(el => el.relationshipType.toUpperCase() === a.payload)
          .find(el => el.subtypes.length === 1);

      newState.deduplicationRequest.relationshipType = RelationshipType[a.payload];
      newState.deduplicationRequest.relationshipSubType = subtypeRelationship ? subtypeRelationship.subtypes[0] : null;
      return newState;
   }

   public static doSetSlaveStatusInARelationship(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetSlaveStatusInARelationship = ObjectsUtils.forceCast<DeduplicationAction_SetSlaveStatusInARelationship>(action);

      newState.groupMaterials.materials = newState.groupMaterials.materials?.map(
          m => ({
              ...m,
              status: m.materialId === a.payload.material.materialId ? a.payload.status : m.status,
          })
      );

      if (a.payload.status !== null && typeof a.payload.status === 'object') {
          a.payload.status = "";
      }

      newState.deduplicationRequest.materialsDeduplication = newState.deduplicationRequest.materialsDeduplication?.map(
          m => ({
              ...m,
              status: m.materialId === a.payload.material.materialId ? a.payload.status : m.status,
          })
      );

      return newState;
   }

    public static doSetStepPage(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_SetStepPage = ObjectsUtils.forceCast<DeduplicationAction_SetStepPage>(action);

        if (a.payload.subStep !== undefined) {
            newState.step = newState.stepper[a.payload.step];
            newState.step.subStepPage = a.payload.subStep;
        } else {
            newState.step = newState.stepper[a.payload.step];
            newState.step.subStepPage = undefined;
        }
        newState.showEnrichedData = false;
        return newState;
    }

    public static doGetRelationshipMaterialsDetailsSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_GetRelationshipMaterialsDetails_Success>(action);

        const inStoreMaterial = new Map(
            (newState.groupMaterials.materials ?? []).map(m => [m.materialId, m])
        );

        newState.groupMaterials.materials = a.payload.materials.map((value) => {
            const existing = inStoreMaterial.get(value.materialId);
            if (existing) {
                return {
                    ...value,
                    // In ADDITIONAL_INFO mode, preserve existing status from additional-info API over relationship-creation API
                    status: newState.viewMode === DeduplicationViewModeEnum.ADDITIONAL_INFO && existing.status
                        ? existing.status
                        : value.status ?? existing.status,
                    selected: true,
                    primary: value.primary ?? existing.primary,
                };
            } else {
                return {
                    ...value,
                    selected: false,
                };
            }
        });

        newState.loading = false;
        updateSelectedDetailsOnState(newState);
        return newState;
    }

    public static doGetSubtypesRelationship(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SubtypesRelationships = ObjectsUtils.forceCast<DeduplicationAction_SubtypesRelationships>(action);
      newState.subtypeRelationship = a.payload;
      newState.loading = true;
      return newState;
   }

    public static doGetSubtypesRelationshipSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_SubtypesRelationships>(action);

        const payload = a.payload ?? [];
        const firstGroup = payload[0];
        const subtypes = firstGroup?.subtypes ?? [];

        if (newState.viewMode === DeduplicationViewModeEnum.ADDITIONAL_INFO &&
            newState.deduplicationRequest.relationshipSubType) {
            const key = newState.deduplicationRequest.relationshipSubType.key;
            newState.deduplicationRequest.relationshipSubType = subtypes.find(s => s.key === key) ?? null;
        } else {
            newState.deduplicationRequest.relationshipSubType = subtypes.length === 1 ? subtypes[0] : null;
        }

        newState.subtypeRelationship = payload;
        newState.loading = false;

        return newState;
    }

    public static doGetSubtypesRelationshipFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
    const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
    newState.loading = false;
    newState.subtypeRelationship = null;
    return newState;
  }

  public static doSelectedSubtypesRelationships(state: DeduplicationState, action: DeduplicationActionTypes) {
    const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
    const a: DeduplicationAction_SelectedSubtypesRelationships = ObjectsUtils.forceCast<DeduplicationAction_SelectedSubtypesRelationships>(action);

    newState.deduplicationRequest.relationshipSubType = a.payload;
    newState.loading = false;
    return newState;
  }

  public static doSetCreatingGoldenRecord(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetCreatingGoldenRecord = ObjectsUtils.forceCast<DeduplicationAction_SetCreatingGoldenRecord>(action);

      newState.deduplicationRequest.isCreatingGoldenRecord = a.payload;
      return newState;
  }

  public static doTogglePrimary(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipCreationTogglePrimary = ObjectsUtils.forceCast<DeduplicationAction_RelationshipCreationTogglePrimary>(action);

      const affectedClient =
        newState.groupMaterials.materials.find(
          (m) => m.materialId === action.payload
        )?.client;
      newState.groupMaterials.materials = newState.groupMaterials.materials.map(
              (m) => ({
                ...m,
                primary:
                  m.materialId === a.payload
                    ? !m.primary
                    : m.client === affectedClient
                    ? false
                    : newState.crossClient
                    ? false
                    : m.primary,
                selected:
                  m.materialId === a.payload
                    ? true
                    : m.selected,
              })
            );
      newState.isSubmittingData = true;
      updateSelectedDetailsOnState(newState);
      return newState;
  }

    public static doToggleSelected(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_RelationshipCreationToggleSelected = ObjectsUtils.forceCast<DeduplicationAction_RelationshipCreationToggleSelected>(action);

      newState.groupMaterials.materials = newState.groupMaterials.materials.map(
              (m) => ({
                ...m,
                selected:
                  m.materialId === a.payload
                    ? !m.selected
                    : m.selected,
                primary:
                  m.materialId === a.payload && m.selected
                    ? false
                    : m.primary,
              })
            );
      newState.isSubmittingData = true;
      updateSelectedDetailsOnState(newState);
      return newState;
  }

   public static doSetRelationshipValidateError(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRelationshipValidateError = ObjectsUtils.forceCast<DeduplicationAction_SetRelationshipValidateError>(action);

      newState.relationshipValidateDrafts = a.payload.relationshipValidateDrafts;
      newState.clientErrors = a.payload.clientValidation;
      newState.crossClient = action.payload.crossClient;
      newState.isValid = false;
      newState.errorRelationshipMessage = null;
      newState.isSubmittingData = false;
      newState.step = {
            ...newState.step,
            isValid: false
      };
      return newState;
   }

   public static doRelationshipIsValid(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRelationshipValidateError = ObjectsUtils.forceCast<DeduplicationAction_SetRelationshipValidateError>(action);

      newState.errorRelationshipMessage = null;
      newState.relationshipValidateDrafts = [];
      newState.clientErrors = null;
      newState.isSubmittingData = false;
      newState.isValid = true;
      newState.crossClient = a.payload.crossClient;
      newState.step = {
         ...newState.step,
         isValid: true
      };
      return newState;
   }

   public static doClearRelationshipValidateError(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);

      newState.relationshipValidateDrafts = [];
      newState.clientErrors = null;
      newState.isValid = false;
      newState.errorRelationshipMessage = null;
      newState.isSubmittingData = false;
      newState.step = {
         ...newState.step,
         isValid: false
      };
      return newState;
   }

    private static doValidateDeduplicationStepSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplicationStep_Success = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplicationStep_Success>(action);

        newState.isValid = true;
        newState.step = {
            ...newState.step,
            isValid: true
        };
        newState.loading = false;
        return newState;
    }

    private static doValidateDeduplicationStepFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplicationStep_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplicationStep_Failure>(action);

        newState.isValid = false;
        newState.step = {
            ...newState.step,
            isValid: false
        };
        return newState;
    }

    private static doValidateDeduplicationFailure(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplication_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplication_Failure>(action);

        newState.isValid = false;
        newState.step = {
            ...newState.step,
            isValid: false
        };
        return newState;
    }

    private static doInitLicenseConfiguration(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_ValidateDeduplication_Failure = ObjectsUtils.forceCast<DeduplicationAction_ValidateDeduplication_Failure>(action);

        newState.licenseConfigurations = a.payload;
        return newState;
    }

    private static doGetEnrichmentMaterialDetailsSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_GetEnrichmentMaterialDetails_Success =
            ObjectsUtils.forceCast<DeduplicationAction_GetEnrichmentMaterialDetails_Success>(action);

        newState.enrichmentMaterialDetails = a.payload;
        newState.originalEnrichmentMaterialDetails = ObjectsUtils.deepClone(a.payload);
        const enrichedFields: Record<string, DeduplicationFieldConfiguration> = {};
        a.payload.forEach((group: DeduplicationEnrichedDataDetails) => {
            group.rows.forEach((row: DeduplicationBasicDataRowDto) => {
                Object.entries(row.primary || {}).forEach(([key, value]) => {
                    enrichedFields[value.id] = { ...value };
                });
            });
        });

        if (!newState.enrichedMaterialsData) {
            newState.enrichedMaterialsData = {};
        }
        newState.enrichedMaterialsData[newState.currentClient] = {
            enrichmentStatus: newState.viewMode === DeduplicationViewModeEnum.ADDITIONAL_INFO ? "EDITED" : "NOT_CHANGED",
            fields: enrichedFields
        };

        newState.showEnrichedData = false;
        newState.loading = false;
        return newState;
    }

    private static doGetEnrichmentPlantDetailsSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_GetEnrichmentPlantDetails_Success =
            ObjectsUtils.forceCast<DeduplicationAction_GetEnrichmentPlantDetails_Success>(action);

        newState.enrichmentPlantDetails = a.payload;
        newState.originalEnrichmentPlantDetails = ObjectsUtils.deepClone(newState.enrichmentPlantDetails);
        const enrichedFields: Record<string, DeduplicationFieldConfiguration> = {};
        newState.enrichmentPlantDetails.forEach((group: DeduplicationEnrichedDataDetails) => {
            group.rows.forEach((row: DeduplicationBasicDataRowDto) => {
                Object.entries(row.primary || {}).forEach(([key, value]) => {
                    enrichedFields[value.id] = { ...value };
                });
            });
        });

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode || '';

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient]) {
            newState.enrichedPlantData[newState.currentClient] = {};
        }
        newState.enrichedPlantData[newState.currentClient][plantKey] = {
            enrichmentStatus: "NOT_CHANGED",
            fields: enrichedFields,
            extension: newState.currentPlant?.extension ?? false
        };

        newState.showEnrichedData = false;
        newState.loading = false;
        return newState;
    }

    private static doPlantEnrichmentSetupSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_PlantEnrichmentSetup_Success>(action);
        newState.deduplicationSetup = a.payload;
        newState.loading = false;
        return newState;
    }

    public static doUpdateCurrentClient(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_UpdateCurrentClient = ObjectsUtils.forceCast<DeduplicationAction_UpdateCurrentClient>(action);
      newState.currentClient = a.payload;
      return newState;
    }

    public static doUpdateCurrentPlant(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_UpdateCurrentPlant = ObjectsUtils.forceCast<DeduplicationAction_UpdateCurrentPlant>(action);
        newState.currentClient = a.payload.client;
        newState.currentPlant = a.payload.plantInformation;
        return newState;
    }

    public static doOpenAdditionalInfo(state: DeduplicationState, action: DeduplicationActionTypes) {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.viewMode = DeduplicationViewModeEnum.ADDITIONAL_INFO;
        newState.processId = action.payload.processId;
        return newState;
    }

    public static doOpenAdditionalInfoSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_OpenAdditionalInfo_Success = ObjectsUtils.forceCast<DeduplicationAction_OpenAdditionalInfo_Success>(action);
      const resp = a.payload;
      newState.viewMode = DeduplicationViewModeEnum.ADDITIONAL_INFO;

      newState.additionalInfoData = resp;
      newState.groupMaterials.materials = resp.materials
        .map(m => ({
          materialId: m.materialId,
          status: m.status,
          primary: m.role === RelationshipRole.PRIMARY,
          selected: true,
          client: m.client,
          materialRelationship: {
            materialId: m.materialId,
            materialRelationshipsDetails: []
          }
      } as RelationshipMaterialInfo));
      newState.selectedSubtypes = {
          key: resp.subType,
          value: resp.subType
      };
      newState.deduplicationRequest = {
        ...newState.deduplicationRequest,
        relationshipType: resp.type as RelationshipType,
        relationshipSubType: {
          key: resp.subType,
          value: resp.subType
        },
        materialsInRelationship: resp.materials
          .map(m => ({
            materialId: m.materialId,
            // status: m.status,
            materialStatus: m.status,
            primary: m.role === RelationshipRole.PRIMARY,
            selected: true,
            role: m.role
          } as MaterialInRelationship))
      };
      updateSelectedDetailsOnState(newState);
      return newState;
    }

    public static doGetMasterDataStatusSuccess(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_GetMasterDataStatus_Success = ObjectsUtils.forceCast<DeduplicationAction_GetMasterDataStatus_Success>(action);

      newState.availableStatusesByClient = a.payload;

      if (newState.viewMode === DeduplicationViewModeEnum.ADDITIONAL_INFO) {
          // In ADDITIONAL_INFO mode, preserve existing status values from additional-info API
          newState.deduplicationRequest.materialsDeduplication = newState.deduplicationRequest.materialsDeduplication?.map(m => {
            const clientData = a.payload?.[m.client];
            if (clientData?.length === 1 && !m.status) {
              return {
                ...m,
                status: clientData[0].key,
                statusDescription: clientData[0].text
              };
            }
            return m;
          });

      } else {
          newState.deduplicationRequest.materialsDeduplication = newState.deduplicationRequest.materialsDeduplication?.map(m => {
            const clientData = a.payload?.[m.client];

            if (clientData) {
              if (clientData.length === 1) {
                return {
                  ...m,
                  status: clientData[0].key
                };
              }

              if (clientData.some(s => s.key === m.status)) {
                return m;
              }
            }

            return {
              ...m,
              status: ""
            };
          });
      }
      if (newState.deduplicationRequest.materialsDeduplication) {
        const statusMap = new Map(
          newState.deduplicationRequest.materialsDeduplication.map(m => [m.materialId, m.status])
        );
        newState.groupMaterials.materials = newState.groupMaterials.materials?.map(m => {
          const newStatus = statusMap.get(m.materialId) ?? m.status;
          const statusDescription = newStatus ?
            a.payload?.[m.client]?.find(opt => opt.key === newStatus)?.text
              || newStatus : m.statusDescription;
          return {
            ...m,
            status: newStatus,
            statusDescription
          };
        });
      }

      newState.loading = false;
      return newState;
    }

    public static doSetRequestNote(state: DeduplicationState, action: DeduplicationActionTypes) {
      const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
      const a: DeduplicationAction_SetRequestNote = ObjectsUtils.forceCast<DeduplicationAction_SetRequestNote>(action);

      newState.deduplicationRequest.note = a.payload;
      return newState;
    }

    public static doUpdateEnrichmentFieldSelection(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UpdateEnrichmentFieldSelection>(action);

        if (!newState.enrichedMaterialsData) {
            newState.enrichedMaterialsData = {};
        }
        if (!newState.enrichedMaterialsData[newState.currentClient]) {
            newState.enrichedMaterialsData[newState.currentClient] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {}
            };
        }

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                if (row.id === a.payload.fieldConfiguration.id) {
                    const secondaryField = row.secondaries[a.payload.materialKey];
                    applyMaterialSecondarySelection(row, secondaryField, a.payload.materialKey, true, newState.enrichedMaterialsData[newState.currentClient]);
                }
            });
        });

        newState.isEditApplied = false;
        return newState;
    }

    public static doBulkSelectEnrichmentColumn(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_BulkSelectEnrichmentColumn>(action);
        const columnMaterialId = a.payload.columnMaterialId;
        const selected = a.payload.selected;

        if (!newState.enrichedMaterialsData) {
            newState.enrichedMaterialsData = {};
        }
        if (!newState.enrichedMaterialsData[newState.currentClient]) {
            newState.enrichedMaterialsData[newState.currentClient] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {}
            };
        }

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                const secondaryField = row.secondaries[columnMaterialId];
                if (secondaryField && secondaryField.editable) {
                    secondaryField.selected = selected;
                    applyMaterialSecondarySelection(row, secondaryField, columnMaterialId, false, newState.enrichedMaterialsData[newState.currentClient]);
                }
            });
        });
        return newState;
    }

    public static doUpdateEnrichmentPrimaryFieldValue(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UpdateEnrichmentPrimaryFieldValue>(action);

        if (!newState.enrichedMaterialsData) {
            newState.enrichedMaterialsData = {};
        }
        if (!newState.enrichedMaterialsData[newState.currentClient]) {
            newState.enrichedMaterialsData[newState.currentClient] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {}
            };
        }

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                const primaryKeys = Object.keys(row.primary || {});
                const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                if (primaryField?.id === a.payload.fieldId) {
                    const fieldKey = primaryKeys[0];
                    row.primary[fieldKey] = {
                        ...primaryField,
                        value: a.payload.value
                    };
                    newState.enrichedMaterialsData[newState.currentClient].fields[primaryField.id] = {
                        ...primaryField,
                        value: a.payload.value
                    };
                    newState.enrichedMaterialsData[newState.currentClient].enrichmentStatus = "EDITED";
                }
            });
        });

        return newState;
    }

    public static doUpdateEnrichmentFieldErrors(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a: DeduplicationAction_UpdateEnrichmentFieldErrors = ObjectsUtils.forceCast<DeduplicationAction_UpdateEnrichmentFieldErrors>(action);

        const fieldErrors: { [fieldId: string]: string[] } = {};

        action.payload.materialFormState?.fieldStatus?.forEach((fieldStatus: any) => {
            if (fieldStatus.status === 'error') {
                const fieldId = fieldStatus.field;
                if (!fieldErrors[fieldId]) {
                    fieldErrors[fieldId] = [];
                }
                fieldErrors[fieldId].push(fieldStatus.statusMessage);
            }
        });

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                Object.entries(row.primary || {}).forEach(([, fieldConfig]) => {
                    const fieldId = fieldConfig.id;
                    if (fieldErrors[fieldId]) {
                        fieldConfig.hasError = true;
                        fieldConfig.errors = fieldErrors[fieldId];
                    } else {
                        fieldConfig.hasError = false;
                        fieldConfig.errors = [];
                    }
                });
            });
        });

        newState.isEditApplied = true;
        return newState;
    }

    public static doUpdatePlantEnrichmentFieldSelection(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UpdatePlantEnrichmentFieldSelection>(action);

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode;

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient]) {
            newState.enrichedPlantData[newState.currentClient] = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient][plantKey]) {
            newState.enrichedPlantData[newState.currentClient][plantKey] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {},
                extension: false
            };
        }

        newState.enrichmentPlantDetails?.forEach(group => {
            group.rows.forEach(row => {
                if (row.id === a.payload.fieldConfiguration.id) {
                    const secondaryField = row.secondaries[a.payload.materialKey];
                    applyPlantSecondarySelection(row, secondaryField, a.payload.materialKey, true, newState.enrichedPlantData[newState.currentClient][plantKey]);
                }
            });
        });

        return newState;
    }

    public static doBulkSelectPlantEnrichmentColumn(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_BulkSelectPlantEnrichmentColumn>(action);
        const columnMaterialId = a.payload.columnMaterialId;
        const selected = a.payload.selected;

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode ;

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient]) {
            newState.enrichedPlantData[newState.currentClient] = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient][plantKey]) {
            newState.enrichedPlantData[newState.currentClient][plantKey] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {},
                extension: false
            };
        }

        newState.enrichmentPlantDetails?.forEach(group => {
            group.rows.forEach(row => {
                const secondaryField = row.secondaries[columnMaterialId];
                if (secondaryField && secondaryField.editable) {
                    secondaryField.selected = selected;
                    applyPlantSecondarySelection(row, secondaryField, columnMaterialId, false, newState.enrichedPlantData[newState.currentClient][plantKey]);
                }
            });
        });
        return newState;
    }

    public static doUpdatePlantEnrichmentPrimaryFieldValue(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UpdatePlantEnrichmentPrimaryFieldValue>(action);

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode || '';

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient]) {
            newState.enrichedPlantData[newState.currentClient] = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient][plantKey]) {
            newState.enrichedPlantData[newState.currentClient][plantKey] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {},
                extension: false
            };
        }

        newState.enrichmentPlantDetails?.forEach(group => {
            group.rows.forEach(row => {
                const primaryKeys = Object.keys(row.primary || {});
                const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                if (primaryField?.id === a.payload.fieldId) {
                    const fieldKey = primaryKeys[0];
                    newState.enrichedPlantData[newState.currentClient][plantKey].fields[fieldKey] = {
                        ...primaryField,
                        value: a.payload.value
                    };
                    newState.enrichedPlantData[newState.currentClient][plantKey].enrichmentStatus = "EDITED";
                }
            });
        });

        return newState;
    }

    public static doUncheckPlantSecondaryFieldsForPrimaryField(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UncheckPlantSecondaryFieldsForPrimaryField>(action);
        const fieldId = a.payload;

        // Get plant code from current step
        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode;

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient]) {
            newState.enrichedPlantData[newState.currentClient] = {};
        }
        if (!newState.enrichedPlantData[newState.currentClient][plantKey]) {
            newState.enrichedPlantData[newState.currentClient][plantKey] = {
                enrichmentStatus: "NOT_CHANGED",
                fields: {},
                extension: false
            };
        }

        newState.enrichmentPlantDetails?.forEach(group => {
            group.rows.forEach(row => {
                const primaryKeys = Object.keys(row.primary || {});
                const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                if (primaryField?.id === fieldId) {
                    Object.keys(row.secondaries || {}).forEach(materialKey => {
                        const secondaryField = row.secondaries[materialKey];
                        if (secondaryField && secondaryField.selected) {
                            secondaryField.selected = false;
                            applyPlantSecondarySelection(row, secondaryField, materialKey, false, newState.enrichedPlantData[newState.currentClient][plantKey]);
                        }
                    });
                }
            });
        });

        return newState;
    }

    public static doUncheckSecondaryFieldsForPrimaryField(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_UncheckSecondaryFieldsForPrimaryField>(action);
        const fieldId = a.payload;

        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                if (row.id === fieldId) {
                    Object.keys(row.secondaries).forEach(materialKey => {
                        const secondaryField = row.secondaries[materialKey];
                        if (secondaryField && secondaryField.selected) {
                            secondaryField.selected = false;
                        }
                    });
                }
            });
        });

        return newState;
    }

    public static doClearStatusValues(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.groupMaterials?.materials?.forEach(material => {
            delete material.status;
            delete material.statusDescription;
        });

        newState.deduplicationRequest.materialsDeduplication?.forEach(material => {
            delete material.status;
            delete material.statusDescription;
        });
        return newState;
    }

    public static doClearAllData(state: DeduplicationState): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);

        newState.deduplicationRequest = {
            ...newState.deduplicationRequest,
            note: undefined,
            creatingGoldenRecord: false,
            invalidField: [],
            isCreatingGoldenRecord: false,
            materialStatus: undefined,
            selected: false,
            status: undefined,
            plantChanges: undefined,
            materialsInRelationship: newState.deduplicationRequest?.materialsInRelationship?.map(material => ({
                ...material,
                role: null,
                primary: false,
                selected: false,
                status: null,
                materialStatus: null
            }))
        };
        newState.enrichmentMaterialDetails = undefined;
        newState.enrichmentPlantDetails = undefined;
        newState.enrichedMaterialsData = {};
        newState.enrichedPlantData = {};
        newState.originalEnrichmentMaterialDetails = undefined;
        newState.originalEnrichmentPlantDetails = undefined;
        newState.currentClient = '';
        newState.showEnrichedData = false;
        newState.note = undefined;
        newState.errorMessage = '';
        newState.errorRelationshipMessage = '';
        newState.clientErrors = undefined;
        newState.availableStatusesByClient = undefined;
        newState.groupClients = [];
        newState.deduplicationSetup = undefined;

        newState.groupMaterials.materials = newState.groupMaterials.materials?.map(
            m => ({
                ...m,
                status: undefined,
                statusDescription: undefined,
                selected: false,
                primary: false,
                materialStatus: undefined,
                errorMessage: ''
            })
        );

        newState.loading = false;
        return newState;
    }

    public static doToggleShowEnrichedData(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_ToggleShowEnrichedData>(action);

        newState.showEnrichedData = a.payload;

        if (a.payload) {
            if (!newState.originalEnrichmentMaterialDetails) {
                newState.originalEnrichmentMaterialDetails = ObjectsUtils.deepClone(newState.enrichmentMaterialDetails);
            }
            if (newState.enrichedMaterialsData && newState.currentClient && newState.enrichmentMaterialDetails) {
                const enrichedClientData = newState.enrichedMaterialsData[newState.currentClient];
                if (enrichedClientData?.fields) {
                    newState.enrichmentMaterialDetails = newState.enrichmentMaterialDetails.map(group => ({
                        ...group,
                        rows: group.rows.map(row => {
                            if (!row.primary) {
                                return row;
                            }

                            const primaryKeys = Object.keys(row.primary);
                            const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                            if (!primaryField) {
                                return row;
                            }

                            const enrichedField = enrichedClientData.fields[primaryField.id];
                            if (!enrichedField) {
                                return row;
                            }

                            const materialCode = primaryKeys[0];
                            return {
                                ...row,
                                primary: {
                                    ...row.primary,
                                    [materialCode]: {
                                        ...primaryField,
                                        value: enrichedField.value
                                    }
                                }
                            };
                        })
                    }));
                }
            }
        } else {
            if (newState.originalEnrichmentMaterialDetails) {
                const currentSelectionStates = new Map<string, Map<string, boolean>>();
                newState.enrichmentMaterialDetails?.forEach(group => {
                    group.rows.forEach(row => {
                        if (row.secondaries) {
                            const rowSelections = new Map<string, boolean>();
                            Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
                                if (secondary && secondary.selected !== undefined) {
                                    rowSelections.set(materialKey, secondary.selected);
                                }
                            });
                            if (rowSelections.size > 0) {
                                currentSelectionStates.set(row.id, rowSelections);
                            }
                        }
                    });
                });
                newState.enrichmentMaterialDetails = ObjectsUtils.deepClone(newState.originalEnrichmentMaterialDetails);
                newState.enrichmentMaterialDetails?.forEach(group => {
                    group.rows.forEach(row => {
                        const rowSelections = currentSelectionStates.get(row.id);
                        if (rowSelections && row.secondaries) {
                            Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
                                if (secondary && rowSelections.has(materialKey)) {
                                    secondary.selected = rowSelections.get(materialKey);
                                }
                            });
                        }
                    });
                });
            }
        }

        return newState;
    }

    public static doToggleShowEnrichedPlantData(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const a = ObjectsUtils.forceCast<DeduplicationAction_ToggleShowEnrichedData>(action);

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode || '';

        newState.showEnrichedData = a.payload;

        if (a.payload) {
            if (!newState.originalEnrichmentPlantDetails) {
                newState.originalEnrichmentPlantDetails = ObjectsUtils.deepClone(newState.enrichmentPlantDetails);
            }
            if (newState.enrichedPlantData && newState.currentClient && newState.enrichmentPlantDetails && plantKey) {
                const enrichedClientData = newState.enrichedPlantData[newState.currentClient];
                const enrichedPlantFields = enrichedClientData?.[plantKey];

                if (enrichedPlantFields?.fields) {
                    newState.enrichmentPlantDetails = newState.enrichmentPlantDetails.map(group => ({
                        ...group,
                        rows: group.rows.map(row => {
                            const enrichedField = enrichedPlantFields.fields[row.id];
                            if (!enrichedField || !row.primary) {
                                return row;
                            }
                            const materialCode = Object.keys(row.primary).find(code => {
                                const field = row.primary?.[code];
                                return field?.id === enrichedField.id;
                            });
                            if (!materialCode) {
                                return row;
                            }
                            const primaryField = row.primary[materialCode];
                            return {
                                ...row,
                                primary: {
                                    ...row.primary,
                                    [materialCode]: {
                                        ...primaryField,
                                        value: enrichedField.value
                                    }
                                }
                            };
                        })
                    }));
                }
            }
        } else {
            if (newState.originalEnrichmentPlantDetails) {
                const currentSelectionStates = new Map<string, Map<string, boolean>>();
                newState.enrichmentPlantDetails?.forEach(group => {
                    group.rows.forEach(row => {
                        if (row.secondaries) {
                            const rowSelections = new Map<string, boolean>();
                            Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
                                if (secondary && secondary.selected !== undefined) {
                                    rowSelections.set(materialKey, secondary.selected);
                                }
                            });
                            if (rowSelections.size > 0) {
                                currentSelectionStates.set(row.id, rowSelections);
                            }
                        }
                    });
                });
                newState.enrichmentPlantDetails = ObjectsUtils.deepClone(newState.originalEnrichmentPlantDetails);
                newState.enrichmentPlantDetails?.forEach(group => {
                    group.rows.forEach(row => {
                        const rowSelections = currentSelectionStates.get(row.id);
                        if (rowSelections && row.secondaries) {
                            Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
                                if (secondary && rowSelections.has(materialKey)) {
                                    secondary.selected = rowSelections.get(materialKey);
                                }
                            });
                        }
                    });
                });
            }
        }

        return newState;
    }

    public static doSkipPlantStepSuccess(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode;

        if (!newState.enrichedPlantData) {
            newState.enrichedPlantData = {};
        }
        if (newState.enrichedPlantData[newState.currentClient] && plantKey) {
            newState.enrichedPlantData[newState.currentClient][plantKey] = {
                ...newState.enrichedPlantData[newState.currentClient][plantKey],
                enrichmentStatus: "SKIPPED"
            };
        }
        newState.loading = false;
        newState.showEnrichedData = false;
        return newState;
    }

    public static doMoveAllSelectedValues(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);
        newState.loading = true;

        if (!newState.enrichedMaterialsData || !newState.enrichedMaterialsData[newState.currentClient]) {
            return newState;
        }

        const enrichedData = newState.enrichedMaterialsData[newState.currentClient];
        newState.enrichmentMaterialDetails?.forEach(group => {
            group.rows.forEach(row => {
                const selectedSecondaries = Object.entries(row.secondaries || {}).filter(([, secondary]) =>
                    secondary && secondary.selected && secondary.editable && secondary.value && secondary.value !== ''
                );

                if (selectedSecondaries.length > 0) {
                    const primaryKeys = Object.keys(row.primary || {});
                    const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                    if (primaryField && enrichedData.fields[row.id]) {
                        const enrichedPrimaryField = enrichedData.fields[row.id];
                        const [, selectedSecondary] = selectedSecondaries[0];
                        if (enrichedPrimaryField.oldValue === undefined) {
                            enrichedPrimaryField.oldValue = enrichedPrimaryField.value;
                        }

                        enrichedPrimaryField.value = `${selectedSecondary.value}`;
                        primaryField.value = `${selectedSecondary.value}`;
                        Object.entries(row.secondaries).forEach(([, secondary]) => {
                            if (secondary && secondary.selected) {
                                secondary.selected = false;
                            }
                        });
                    }
                }
            });
        });

        enrichedData.enrichmentStatus = "EDITED";

        newState.deduplicationRequest.materialsDeduplication
          .filter(material => material.primary && material.client === newState.currentClient)
          .forEach(material => {
              material.isEdited = true;
        });

        newState.isEditApplied = true;
        newState.loading = false;
        return newState;
    }

    public static doMoveAllSelectedPlantValues(
        state: DeduplicationState,
        action: DeduplicationActionTypes
    ): DeduplicationState {
        const newState: DeduplicationState = ObjectsUtils.deepClone<DeduplicationState>(state);

        const plantInformation = getPlantInformationFromStepPage(newState.step);
        const plantKey = plantInformation?.plantCode || '';

        if (!newState.enrichedPlantData || !newState.enrichedPlantData[newState.currentClient] || !newState.enrichedPlantData[newState.currentClient][plantKey]) {
            return newState;
        }

        const enrichedData = newState.enrichedPlantData[newState.currentClient][plantKey];
        newState.enrichmentPlantDetails?.forEach(group => {
            group.rows.forEach(row => {
                const selectedSecondaries = Object.entries(row.secondaries || {}).filter(([, secondary]) =>
                    secondary && secondary.selected && secondary.editable && secondary.value && secondary.value !== ''
                );

                if (selectedSecondaries.length > 0) {
                    const primaryKeys = Object.keys(row.primary || {});
                    const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

                    if (primaryField && enrichedData.fields[row.id]) {
                        const enrichedPrimaryField = enrichedData.fields[row.id];
                        const [, selectedSecondary] = selectedSecondaries[0];
                        if (enrichedPrimaryField.oldValue === undefined) {
                            enrichedPrimaryField.oldValue = enrichedPrimaryField.value;
                        }
                        enrichedPrimaryField.value = `${selectedSecondary.value}`;
                        primaryField.value = `${selectedSecondary.value}`;
                        Object.entries(row.secondaries).forEach(([, secondary]) => {
                            if (secondary && secondary.selected) {
                                secondary.selected = false;
                            }
                        });
                    }
                }
            });
        });

        enrichedData.enrichmentStatus = "EDITED";

        return newState;
    }
}
